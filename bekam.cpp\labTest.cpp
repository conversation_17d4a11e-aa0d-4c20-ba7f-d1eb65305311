#include <iostream> // For input/output operations (cin, cout)
#include <fstream>  // For file stream operations (fstream)
#include <limits>   // For numeric_limits to properly clear input buffer

// It's generally good practice to explicitly use std:: for common elements
// or use 'using namespace std;' if you're comfortable with it for smaller programs.
// For this example, I'll keep 'using namespace std;' as in your original code.
using namespace std;

// --- Student Structure ---
// Defines the blueprint for a Student record.
struct Student {
    int roll;         // Student's roll number
    char name[25];    // Student's name (fixed-size character array)
    float marks;      // Student's marks
}; // No global instance here; we'll create Student objects as needed.

// --- Function to Get Student Data ---
// Takes a reference to a Student object and fills its members with user input.
void getStudentData(Student& s) {
    cout << "\n--- Enter Student Details ---" << endl;
    cout << "Enter Roll Number: ";
    cin >> s.roll;

    // Clear the input buffer. This is crucial after reading a number (like int or float)
    // and before reading a line of text (like name) with cin.getline().
    // It discards the leftover newline character from the buffer.
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    cout << "Enter Name (up to 24 characters): ";
    // Read the full line for the name, including spaces.
    // The '25' specifies the maximum number of characters to read, including the null terminator.
    cin.getline(s.name, 25);

    cout << "Enter Marks: ";
    cin >> s.marks;
}

// --- Function to Add a Record to the File ---
// Opens the file in binary append mode and writes one Student record.
void addRecordToFile() {
    // Open "Student.txt" in output, append, and binary modes.
    // ios::out: Enable writing.
    // ios::app: Append to the end of the file if it exists (don't overwrite).
    // ios::binary: Treat the file as a raw sequence of bytes, crucial for struct I/O.
    fstream outFile("Student.txt", ios::out | ios::app | ios::binary);

    // Always check if the file opened successfully.
    if (!outFile.is_open()) {
        cerr << "Error: Could not open 'Student.txt' for writing." << endl;
        return; // Exit the function if file opening failed.
    }

    cout << "File 'Student.txt' opened successfully for appending." << endl;

    Student currentStudent; // Create a local Student object for input.
    getStudentData(currentStudent); // Get data from the user into this object.

    // Write the binary representation of the 'currentStudent' object to the file.
    // reinterpret_cast<char*>(&currentStudent): Converts the Student* to a char* (byte pointer).
    // sizeof(currentStudent): Gets the size of the Student structure in bytes.
    outFile.write(reinterpret_cast<char*>(&currentStudent), sizeof(currentStudent));

    outFile.close(); // Close the file stream to save changes.
    cout << "Record added to 'Student.txt'." << endl;
}

// --- Function to Display All Records from the File ---
// Opens the file in binary input mode and reads/displays all Student records.
void displayAllRecords() {
    // Open "Student.txt" in input and binary modes.
    fstream inFile("Student.txt", ios::in | ios::binary);

    if (!inFile.is_open()) {
        cerr << "Error: Could not open 'Student.txt' for reading." << endl;
        // If the file doesn't exist yet, it's not an error but just no records.
        // You might want a different message here if the file genuinely couldn't be accessed.
        cout << "No records to display (file might not exist or is empty)." << endl;
        return;
    }

    cout << "\n--- Displaying All Student Records ---" << endl;
    Student s; // Create a local Student object to read data into.
    bool recordsFound = false;

    // Loop to read records until the end of the file is reached or an error occurs.
    // The 'read' function returns a reference to the stream, which evaluates to true on success.
    while (inFile.read(reinterpret_cast<char*>(&s), sizeof(s))) {
        recordsFound = true;
        cout << "\n--------------------------" << endl;
        cout << "Roll: " << s.roll << endl;
        cout << "Name: " << s.name << endl;
        cout << "Marks: " << s.marks << endl;
        cout << "--------------------------" << endl;
    }

    inFile.close(); // Close the file stream.

    if (!recordsFound) {
        cout << "No records found in 'Student.txt'." << endl;
    }
    cout << "\n--- End of Records ---" << endl;
}

// --- Main Function ---
// Controls the program flow, allowing users to add multiple records.
int main() {
    char choice;

    do {
        addRecordToFile(); // Call function to add a new record.

        cout << "\nDo you want to add more records? (y/n): ";
        // Clear the buffer after reading 'marks' and before reading 'choice'.
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        cin.get(choice); // Read user's choice.

    } while (choice == 'y' || choice == 'Y'); // Continue if user enters 'y' or 'Y'.

    cout << "\nFinished adding records." << endl;

    // After adding, let's display all records to confirm they were saved.
    displayAllRecords();

    cout << "\nProgram terminated successfully." << endl;

    return 0; // Indicate successful program execution.
}