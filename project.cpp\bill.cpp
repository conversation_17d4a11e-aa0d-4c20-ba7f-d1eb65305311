#include <iostream>
#include <fstream>
#include <iomanip>
#include <string>
#include <vector>
#include <ctime>
#include <sstream>
#include <limits>
#include <cmath> // For fabs, isnan, isinf

using namespace std;

// --- Structures ---

struct Bill
{
    int bill_id;
    int patient_id;
    double consultation_fee;
    double medication_fee;
    double lab_fee;
    double room_charge;
    double total_amount;
    string card_number;
    string cvc;
    string expiry_date;
    string status;
    string bill_date;
};

// --- Utility Functions ---

// Validate input with a template for various types
template <typename T>
T getValidatedInput(const string& prompt)
{
    T value;
    cout << prompt;
    while (!(cin >> value))
    {
        cout << "Invalid input. Please enter a valid number: ";
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n'); // Clear the rest of the line
    return value;
}

// Get the current date and time in a formatted string
string getCurrentDateTime()
{
    time_t now = time(0);
    tm *ltm = localtime(&now);
    char buf[80];
    strftime(buf, sizeof(buf), "%Y-%m-%d %H:%M:%S", ltm);
    return string(buf);
}

// Write header to file if it's empty
void writeHeaderIfEmpty(fstream &file)
{
    file.seekp(0, ios::end); // Go to the end of the file
    if (file.tellp() == 0)   // If at the beginning, file is empty
    {
        file << left
             << setw(10) << "Bill ID"
             << setw(15) << "Patient ID"
             << setw(20) << "Consultation Fee"
             << setw(20) << "Medication Fee"
             << setw(12) << "Lab Fee"
             << setw(14) << "Room Charge"
             << setw(15) << "Total Amount"
             << setw(20) << "Card Number"
             << setw(6) << "CVC"
             << setw(15) << "Expiry Date"
             << setw(12) << "Status"
             << setw(20) << "Bill Date" << endl;
    }
}

// --- Billing System Functions ---

// Generate new bills
vector<Bill> generateBills()
{
    int count = getValidatedInput<int>("Enter number of bills to generate: ");
    vector<Bill> bills;

    // Read existing bill IDs once for efficiency
    vector<int> existingBillIds;
    ifstream readFile("Bill.txt");
    if (readFile)
    {
        string line;
        getline(readFile, line); // Skip header

        while (getline(readFile, line))
        {
            istringstream iss(line);
            int billId;
            // Attempt to read only the bill ID to check for duplicates
            if (iss >> billId) {
                existingBillIds.push_back(billId);
            }
        }
        readFile.close();
    }

    // Open file for appending
    fstream outf("Bill.txt", ios::app);
    if (!outf)
    {
        cerr << "Error opening Bill.txt for writing.\n";
        return bills;
    }

    writeHeaderIfEmpty(outf); // Ensure header is present

    for (int i = 0; i < count; i++)
    {
        Bill b;
        cout << "\n--- Bill " << i + 1 << " ---\n";

        // Get bill ID and check for duplicates
        while (true)
        {
            b.bill_id = getValidatedInput<int>("Bill ID: ");

            bool isDuplicate = false;
            for (int existingId : existingBillIds)
            {
                if (existingId == b.bill_id)
                {
                    isDuplicate = true;
                    break;
                }
            }
            // Also check against bills added in the current session
            if (!isDuplicate) {
                for (const Bill &addedBill : bills) {
                    if (addedBill.bill_id == b.bill_id) {
                        isDuplicate = true;
                        break;
                    }
                }
            }

            if (isDuplicate)
            {
                cout << "Error: Bill ID " << b.bill_id << " already exists. Please use a different ID.\n";
            }
            else
            {
                break; // Valid ID, continue
            }
        }
        
        b.patient_id = getValidatedInput<int>("Patient ID: ");
        
        // Input validation for fees (ensure non-negative)
        do {
            b.consultation_fee = getValidatedInput<double>("Consultation Fee: ");
            if (b.consultation_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
        } while (b.consultation_fee < 0);

        do {
            b.medication_fee = getValidatedInput<double>("Medication Fee: ");
            if (b.medication_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
        } while (b.medication_fee < 0);

        do {
            b.lab_fee = getValidatedInput<double>("Lab Fee: ");
            if (b.lab_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
        } while (b.lab_fee < 0);

        do {
            b.room_charge = getValidatedInput<double>("Room Charge: ");
            if (b.room_charge < 0) cout << "Charge cannot be negative. Please try again.\n";
        } while (b.room_charge < 0);

        b.total_amount = b.consultation_fee + b.medication_fee + b.lab_fee + b.room_charge;
        b.card_number = "-";
        b.cvc = "-";
        b.expiry_date = "-";
        b.status = "unpaid";
        b.bill_date = getCurrentDateTime();

        bills.push_back(b);
        existingBillIds.push_back(b.bill_id); // Add to our tracking list for current session

        outf << left
             << setw(10) << b.bill_id
             << setw(15) << b.patient_id
             << setw(20) << fixed << setprecision(2) << b.consultation_fee
             << setw(20) << fixed << setprecision(2) << b.medication_fee
             << setw(12) << fixed << setprecision(2) << b.lab_fee
             << setw(14) << fixed << setprecision(2) << b.room_charge
             << setw(15) << fixed << setprecision(2) << b.total_amount
             << setw(20) << b.card_number
             << setw(6) << b.cvc
             << setw(15) << b.expiry_date
             << setw(12) << b.status
             << setw(20) << b.bill_date << endl;
    }

    outf.close();
    cout << "\nBills generated and saved successfully.\n";
    return bills;
}

// Update existing bills
void updateGeneratedBills()
{
    int count = getValidatedInput<int>("Enter number of bills to update: ");

    for (int i = 0; i < count; i++)
    {
        int bill_id_to_update = getValidatedInput<int>("Enter Bill ID " + to_string(i + 1) + " to update: ");

        ifstream inFile("Bill.txt");
        if (!inFile)
        {
            cerr << "Error: Bill.txt not found.\n";
            return;
        }

        ofstream tempFile("Temp.txt");
        if (!tempFile) {
            cerr << "Error creating temporary file for update.\n";
            inFile.close();
            return;
        }

        string line;
        bool found = false;

        // Copy header to temp file
        if (getline(inFile, line) && line.find("Bill ID") != string::npos)
        {
            tempFile << line << endl;
        }
        else
        {
            inFile.seekg(0); // If no header, reset file position
        }

        while (getline(inFile, line))
        {
            istringstream iss(line);
            Bill b;
            
            // Read all fields from the line
            string card_num_str, cvc_str, expiry_str, status_str, date_str_part1, date_str_part2;
            
            if (!(iss >> b.bill_id >> b.patient_id >> b.consultation_fee >> b.medication_fee >> b.lab_fee >> b.room_charge >> b.total_amount
                >> card_num_str >> cvc_str >> expiry_str >> status_str >> date_str_part1 >> date_str_part2))
            {
                // If parsing fails for a line, write it as is (might be a header or corrupted line not meant to be parsed)
                tempFile << line << endl; 
                continue;
            }

            b.card_number = card_num_str;
            b.cvc = cvc_str;
            b.expiry_date = expiry_str;
            b.status = status_str;
            b.bill_date = date_str_part1 + " " + date_str_part2;


            if (b.bill_id == bill_id_to_update)
            {
                found = true;
                cout << "\n--- Updating Bill ID: " << bill_id_to_update << " ---\n";
                cout << "Enter new details (enter 0 for numeric fields to keep current value):\n";

                int new_patient_id;
                do {
                    new_patient_id = getValidatedInput<int>("Patient ID [" + to_string(b.patient_id) + "]: ");
                    if (new_patient_id < 0) cout << "Patient ID cannot be negative. Please try again.\n";
                } while (new_patient_id < 0);
                if (new_patient_id != 0) b.patient_id = new_patient_id;


                double new_consultation_fee;
                do {
                    new_consultation_fee = getValidatedInput<double>("Consultation Fee [" + to_string(b.consultation_fee) + "]: ");
                    if (new_consultation_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
                } while (new_consultation_fee < 0);
                if (new_consultation_fee != 0) b.consultation_fee = new_consultation_fee;

                double new_medication_fee;
                do {
                    new_medication_fee = getValidatedInput<double>("Medication Fee [" + to_string(b.medication_fee) + "]: ");
                    if (new_medication_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
                } while (new_medication_fee < 0);
                if (new_medication_fee != 0) b.medication_fee = new_medication_fee;

                double new_lab_fee;
                do {
                    new_lab_fee = getValidatedInput<double>("Lab Fee [" + to_string(b.lab_fee) + "]: ");
                    if (new_lab_fee < 0) cout << "Fee cannot be negative. Please try again.\n";
                } while (new_lab_fee < 0);
                if (new_lab_fee != 0) b.lab_fee = new_lab_fee;

                double new_room_charge;
                do {
                    new_room_charge = getValidatedInput<double>("Room Charge [" + to_string(b.room_charge) + "]: ");
                    if (new_room_charge < 0) cout << "Charge cannot be negative. Please try again.\n";
                } while (new_room_charge < 0);
                if (new_room_charge != 0) b.room_charge = new_room_charge;

                // Recalculate total amount
                b.total_amount = b.consultation_fee + b.medication_fee + b.lab_fee + b.room_charge;
                cout << "Total amount updated to: " << fixed << setprecision(2) << b.total_amount << endl;
            }
            // Write the (possibly updated) record or the original record
            tempFile << left
                     << setw(10) << b.bill_id
                     << setw(15) << b.patient_id
                     << setw(20) << fixed << setprecision(2) << b.consultation_fee
                     << setw(20) << fixed << setprecision(2) << b.medication_fee
                     << setw(12) << fixed << setprecision(2) << b.lab_fee
                     << setw(14) << fixed << setprecision(2) << b.room_charge
                     << setw(15) << fixed << setprecision(2) << b.total_amount
                     << setw(20) << b.card_number
                     << setw(6)  << b.cvc
                     << setw(15) << b.expiry_date
                     << setw(12) << b.status
                     << setw(20) << b.bill_date << endl;
        }

        inFile.close();
        tempFile.close();

        remove("Bill.txt");
        rename("Temp.txt", "Bill.txt");

        if (found)
        {
            cout << "Bill ID " << bill_id_to_update << " updated successfully.\n";
        }
        else
        {
            cout << "Bill ID " << bill_id_to_update << " not found.\n";
        }
    }
}

// Pay a bill
void payBill()
{
    int count = getValidatedInput<int>("Enter the number of bills to pay: ");

    for (int i = 0; i < count; i++)
    {
        int bill_id_to_pay = getValidatedInput<int>("Enter Bill ID " + to_string(i + 1) + " to pay: ");

        ifstream inFile("Bill.txt");
        if (!inFile)
        {
            cerr << "Error: Bill.txt not found.\n";
            return;
        }

        ofstream tempFile("Temp.txt");
        if (!tempFile) {
            cerr << "Error creating temporary file for payment.\n";
            inFile.close();
            return;
        }

        string line;
        bool updated = false;
        bool found = false;

        // Copy header to temp file
        if (getline(inFile, line) && line.find("Bill ID") != string::npos)
        {
            tempFile << line << endl;
        }
        else
        {
            inFile.seekg(0); // If no header, reset file position
        }

        while (getline(inFile, line))
        {
            istringstream iss(line);
            Bill b;

            // Read all fields from the line
            string card_num_str, cvc_str, expiry_str, status_str, date_str_part1, date_str_part2;
            
            if (!(iss >> b.bill_id >> b.patient_id >> b.consultation_fee >> b.medication_fee >> b.lab_fee >> b.room_charge >> b.total_amount
                >> card_num_str >> cvc_str >> expiry_str >> status_str >> date_str_part1 >> date_str_part2))
            {
                // If parsing fails for a line, write it as is
                tempFile << line << endl; 
                continue;
            }

            b.card_number = card_num_str;
            b.cvc = cvc_str;
            b.expiry_date = expiry_str;
            b.status = status_str;
            b.bill_date = date_str_part1 + " " + date_str_part2;
            
            if (b.bill_id == bill_id_to_pay)
            {
                found = true;
                if (b.status == "paid") {
                    cout << "Bill ID " << bill_id_to_pay << " is already paid.\n";
                } else {
                    cout << "Enter card number: ";
                    getline(cin >> ws, b.card_number); // Use ws to consume any leftover whitespace
                    cout << "Enter CVC: ";
                    getline(cin >> ws, b.cvc);
                    cout << "Enter expiry date (MM/YY): ";
                    getline(cin >> ws, b.expiry_date);
                    b.status = "paid";
                    b.bill_date = getCurrentDateTime(); // Update bill date to current date/time
                    updated = true;
                }
            }

            // Write the (possibly updated) record to the temporary file
            tempFile << left
                     << setw(10) << b.bill_id
                     << setw(15) << b.patient_id
                     << setw(20) << fixed << setprecision(2) << b.consultation_fee
                     << setw(20) << fixed << setprecision(2) << b.medication_fee
                     << setw(12) << fixed << setprecision(2) << b.lab_fee
                     << setw(14) << fixed << setprecision(2) << b.room_charge
                     << setw(15) << fixed << setprecision(2) << b.total_amount
                     << setw(20) << b.card_number
                     << setw(6) << b.cvc
                     << setw(15) << b.expiry_date
                     << setw(12) << b.status
                     << setw(20) << b.bill_date << endl;
        }

        inFile.close();
        tempFile.close();

        remove("Bill.txt");
        rename("Temp.txt", "Bill.txt");

        if (updated)
            cout << "Bill ID " << bill_id_to_pay << " paid successfully.\n";
        else if (!found) // Only show this if it wasn't already paid and not found
            cout << "Bill ID " << bill_id_to_pay << " not found.\n";
    }
}

// Delete bills
void deleteBills()
{
    int count = getValidatedInput<int>("Enter number of bills to delete: ");

    for (int i = 0; i < count; i++)
    {
        int bill_id_to_delete = getValidatedInput<int>("Enter Bill ID " + to_string(i + 1) + " to delete: ");

        ifstream inFile("Bill.txt");
        if (!inFile)
        {
            cerr << "Error: Bill.txt not found.\n";
            return;
        }

        ofstream tempFile("Temp.txt");
        if (!tempFile) {
            cerr << "Error creating temporary file for deletion.\n";
            inFile.close();
            return;
        }

        string line;
        bool found = false;

        // Copy header to temp file
        if (getline(inFile, line) && line.find("Bill ID") != string::npos)
        {
            tempFile << line << endl;
        }
        else
        {
            inFile.seekg(0); // If no header, reset file position
        }

        while (getline(inFile, line))
        {
            istringstream iss(line);
            int current_bill_id;
            
            // Attempt to read only the bill ID to check for deletion
            if (iss >> current_bill_id) {
                if (current_bill_id == bill_id_to_delete)
                {
                    found = true;
                    continue; // Skip writing this line to the temp file
                }
            }
            // Write the line if it's not the one to delete, or if parsing failed for its ID
            tempFile << line << endl;
        }

        inFile.close();
        tempFile.close();

        remove("Bill.txt");
        rename("Temp.txt", "Bill.txt");

        if (found)
        {
            cout << "Bill ID " << bill_id_to_delete << " deleted successfully.\n";
        }
        else
        {
            cout << "Bill ID " << bill_id_to_delete << " not found.\n";
        }
    }
}

// View all bills
void viewBills()
{
    ifstream file("Bill.txt");
    if (!file)
    {
        cerr << "Could not open Bill.txt. It might not exist or be empty.\n";
        return;
    }

    string line;
    cout << "\n--- All Bill Records ---\n";
    while (getline(file, line))
    {
        cout << line << endl;
    }
    file.close();
}

// Search for a specific bill
void search()
{
    int count = getValidatedInput<int>("Enter number of bills to search: ");

    for (int i = 0; i < count; i++)
    {
        int bill_id_to_search = getValidatedInput<int>("Enter Bill ID " + to_string(i + 1) + " to search: ");

        ifstream file("Bill.txt");
        if (!file)
        {
            cerr << "Could not open Bill.txt.\n";
            return;
        }

        string line;
        bool found = false;
        getline(file, line); // Skip header

        while (getline(file, line))
        {
            istringstream iss(line);
            int current_bill_id;
            
            if (iss >> current_bill_id) {
                if (current_bill_id == bill_id_to_search)
                {
                    found = true;
                    cout << "\n--- Bill Found ---\n";
                    // Re-print header for single bill view
                    cout << left
                         << setw(10) << "Bill ID"
                         << setw(15) << "Patient ID"
                         << setw(20) << "Consultation Fee"
                         << setw(20) << "Medication Fee"
                         << setw(12) << "Lab Fee"
                         << setw(14) << "Room Charge"
                         << setw(15) << "Total Amount"
                         << setw(20) << "Card Number"
                         << setw(6)  << "CVC"
                         << setw(15) << "Expiry Date"
                         << setw(12) << "Status"
                         << setw(20) << "Bill Date" << endl;
                    cout << line << endl; // Print the found bill's details
                    break;
                }
            }
        }

        file.close();

        if (!found)
        {
            cout << "Bill ID " << bill_id_to_search << " not found.\n";
        }
    }
}

// Clean up corrupted bill data
void cleanupBillFile() {
    ifstream inFile("Bill.txt");
    if (!inFile) {
        cerr << "Error: Bill.txt not found. Nothing to clean.\n";
        return;
    }
    
    ofstream tempFile("Temp.txt");
    if (!tempFile) {
        cerr << "Error creating temporary file for cleanup.\n";
        inFile.close();
        return;
    }

    string line;
    bool headerCopied = false;
    int fixed_count = 0;
    int skipped_count = 0;

    // Copy header
    if (getline(inFile, line) && line.find("Bill ID") != string::npos) {
        tempFile << line << endl;
        headerCopied = true;
    } else {
        inFile.seekg(0); // Reset if no header found, assume file starts with data
    }
    
    // Process each line
    while (getline(inFile, line)) {
        istringstream iss(line);
        Bill b;
        
        // Attempt to parse the bill data rigorously
        string card_num_str, cvc_str, expiry_str, status_str, date_str_part1, date_str_part2;
        
        if (!(iss >> b.bill_id >> b.patient_id >> b.consultation_fee >> b.medication_fee 
             >> b.lab_fee >> b.room_charge >> b.total_amount
             >> card_num_str >> cvc_str >> expiry_str >> status_str >> date_str_part1 >> date_str_part2)) 
        {
            // If parsing fails, this line is corrupted. Skip it.
            cout << "Skipping corrupted line: " << line << endl;
            skipped_count++;
            continue;
        }
        
        // Assign parsed string values
        b.card_number = card_num_str;
        b.cvc = cvc_str;
        b.expiry_date = expiry_str;
        b.status = status_str;
        b.bill_date = date_str_part1 + " " + date_str_part2;

        // Validate total amount - recalculate if it looks suspicious
        double calculatedTotal = b.consultation_fee + b.medication_fee + b.lab_fee + b.room_charge;
        // Check for floating point inaccuracies or clearly wrong totals
        if (fabs(b.total_amount - calculatedTotal) > 0.01 || 
            b.total_amount < 0 || 
            isnan(b.total_amount) || 
            isinf(b.total_amount)) 
        {
            cout << "Fixing incorrect total for Bill ID " << b.bill_id << ". Old total: " 
                 << fixed << setprecision(2) << b.total_amount << ", New total: " 
                 << fixed << setprecision(2) << calculatedTotal << endl;
            b.total_amount = calculatedTotal;
            fixed_count++;
        }
        
        // Basic validation for status (default to "unpaid" if unknown)
        if (b.status != "paid" && b.status != "unpaid") {
            cout << "Fixing invalid status for Bill ID " << b.bill_id << ". Setting to 'unpaid'.\n";
            b.status = "unpaid";
            fixed_count++;
        }

        // Write the (possibly fixed) record
        tempFile << left
                 << setw(10) << b.bill_id
                 << setw(15) << b.patient_id
                 << setw(20) << fixed << setprecision(2) << b.consultation_fee
                 << setw(20) << fixed << setprecision(2) << b.medication_fee
                 << setw(12) << fixed << setprecision(2) << b.lab_fee
                 << setw(14) << fixed << setprecision(2) << b.room_charge
                 << setw(15) << fixed << setprecision(2) << b.total_amount
                 << setw(20) << b.card_number
                 << setw(6)  << b.cvc
                 << setw(15) << b.expiry_date
                 << setw(12) << b.status
                 << setw(20) << b.bill_date << endl;
    }
    
    inFile.close();
    tempFile.close();
    
    // Replace the original file with the cleaned one
    remove("Bill.txt");
    rename("Temp.txt", "Bill.txt");
    
    cout << "\nBill file cleanup complete.\n";
    if (fixed_count > 0) {
        cout << "Fixed " << fixed_count << " bill records.\n";
    }
    if (skipped_count > 0) {
        cout << "Skipped " << skipped_count << " corrupted lines.\n";
    }
    if (fixed_count == 0 && skipped_count == 0) {
        cout << "No issues found.\n";
    }
}

// --- Main Function ---

int main()
{
    int choice;
    do
    {
        cout << "\n--- HOSPITAL BILLING SYSTEM ---\n";
        cout << "1. Generate New Bill(s)\n";
        cout << "2. View All Bills\n";
        cout << "3. Update Existing Bill(s)\n";
        cout << "4. Delete Bill(s)\n";
        cout << "5. Pay Bill(s)\n";
        cout << "6. Search for Bill(s)\n";
        cout << "7. Clean up Bill File\n";
        cout << "0. Exit\n";
        choice = getValidatedInput<int>("Enter your choice: ");

        switch (choice)
        {
        case 1:
            generateBills();
            break;
        case 2:
            viewBills();
            break;
        case 3:
            updateGeneratedBills();
            break;
        case 4:
            deleteBills();
            break;
        case 5:
            payBill();
            break;
        case 6:
            search();
            break;
        case 7:
            cleanupBillFile();
            break;
        case 0:
            cout << "Exiting Hospital Billing System. Goodbye!\n";
            break;
        default:
            cout << "Invalid choice. Please enter a number between 0 and 7.\n";
        }
    } while (choice != 0);

    return 0;
}