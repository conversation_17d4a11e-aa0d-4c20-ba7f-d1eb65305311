#include <iostream>
#include <fstream>
#include <iomanip>
#include <string>
#include <limits>

using namespace std;

// ---
// ## Student Structure
// ---
struct Student
{
    int roll;
    string name;
    float marks;
};

// ---
// ## Function to Get Student Data
// ---
void getdata(Student &stud)
{
    cout << "\nEnter Roll: ";
    while (!(cin >> stud.roll))
    {
        cout << "Invalid input. Please enter a valid number for Roll: ";
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
    }
    cin.ignore(numeric_limits<streamsize>::max(), '\n');

    cout << "Enter Name: ";
    getline(cin, stud.name);

    cout << "Enter Marks: ";
    while (!(cin >> stud.marks))
    {
        cout << "Invalid input. Please enter a valid number for Marks: ";
        cin.clear();
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
    }
}

// ## Function to Add a Student Record (MODIFIED to add header if file is new)
// ---
void AddRecord()
{
    Student stud;
    getdata(stud);

    // Open file in append mode.
    // We use ios::ate | ios::app for a specific reason:
    // ios::ate (at end) positions the stream at the end of the file upon opening.
    // This allows us to check if the file is empty *before* appending data.
    ofstream outf("Student.txt", ios::app | ios::ate);
    if (!outf)
    {
        cerr << "Error: Could not open file for writing.\n";
        return;
    }

    // Check if the file is empty (i.e., if the file pointer is at the beginning)
    // before writing any new data. If it is, write the header.
    // tellp() returns the current position of the put pointer.
    if (outf.tellp() == 0) // If the file is empty
    {
        outf << "Roll\tName\tMarks" << endl; // Write the header
    }

    // Now write the student data.
    outf << stud.roll << "\t\t" << stud.name << "\t\t" << stud.marks << endl;

    outf.close();
    cout << "Record added successfully!\n";
}

// ## Function to Display All Student Records
// ---

void DisplayRecords()
{
    ifstream inf("Student.txt");
    if (!inf)
    {
        cerr << "Error: Could not open file for reading. File might not exist or is empty.\n";
        return;
    }

    // --- Read and discard the header line ---
    string headerLine;
    getline(inf, headerLine);
    // ----------------------------------------

    Student stud;
    cout << "\n--- Student Records ---\n";
    cout << "========================\n";
    cout << left << setw(10) << "Roll" << setw(25) << "Name" << setw(10) << "Marks" << endl;
    cout << "--------------------------------------------------\n";

    while (inf >> stud.roll >> stud.name >> stud.marks)
    {
        cout << left << setw(10) << stud.roll << setw(25) << stud.name << setw(10) << stud.marks << endl;
    }

    if (inf.fail() && !inf.eof())
    {
        cerr << "Error: Data corruption detected in file. Some records might be unreadable.\n";
    }

    inf.close();
    cout << "--------------------------------------------------\n";
    cout << "End of records.\n";
}

// ## Main Function
// ---
int main()
{
    char ch;
    do
    {
        AddRecord();

        cout << "\nWant to add more records? (y/n): ";
        cin >> ch;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        if (tolower(ch) != 'y' && tolower(ch) != 'n')
        {
            cout << "Invalid input. Please enter 'y' or 'n'.\n";
        }

    } while (tolower(ch) == 'y');

    DisplayRecords();

    return 0;
}