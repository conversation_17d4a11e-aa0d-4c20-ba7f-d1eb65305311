#include <iostream>
#include <fstream>
#include <cstring>

using namespace std;

struct students {
    int id, Rank;
    char name[50];  // Fixed-size array for binary storage
    float cgpa;
};

int main() {
    int n;
    cout << "How many students? ";
    cin >> n;

    students s[n];
    for (int i = 0; i < n; ++i) {
        cout << "Enter student " << i + 1 << " id: ";
        cin >> s[i].id;

        cout << "Enter student " << i + 1 << " name: ";
        cin.ignore();
        memset(s[i].name, 0, sizeof(s[i].name)); // Prevent garbage data
        cin.getline(s[i].name, 50);

        cout << "Enter student " << i + 1 << " cgpa: ";
        cin >> s[i].cgpa;
    }

    // Rank calculation
    for (int i = 0; i < n; i++) {
        s[i].Rank = 1;
        for (int j = 0; j < n; j++) {
            if (s[j].cgpa > s[i].cgpa) s[i].Rank++;
        }
    }

    // Writing student data to binary file
    ofstream stf("Student.dat", ios::binary | ios::app);
    if (!stf) {
        cout << "Error opening file for writing!" << endl;
        return 1;
    }

    for (int j = 0; j < n; j++) {
        stf.write(reinterpret_cast<char*>(&s[j]), sizeof(students));
    }
    stf.close();

    // Reading last student record from binary file
    ifstream stfi("Student.dat", ios::binary);
    if (!stfi) {
        cout << "Error opening file for reading!" << endl;
        return 1;
    }

    stfi.seekg(-sizeof(students), ios::end); // Move to last student record
    students lastStudent;
    stfi.read(reinterpret_cast<char*>(&lastStudent), sizeof(students));

    // Convert last student record into a formatted string
    string lastLine = to_string(lastStudent.id) + " " + lastStudent.name + " " +
                      to_string(lastStudent.cgpa) + " " + to_string(lastStudent.Rank);

    int midPosition = lastLine.length() / 2;
    cout << "The length of the last record: " << lastLine.length() << endl;
    cout << "The middle position of the last record: " << midPosition << endl;
    cout << "Middle character of last record: " << lastLine[midPosition] << endl;

    stfi.close();

    // Read and display all students from the binary file
    ifstream stfr("Student.dat", ios::binary);
    students tempStudent;

    cout << "Id   Name   Cgpa   Rank" << endl;
    while (stfr.read(reinterpret_cast<char*>(&tempStudent), sizeof(students))) {
        cout << tempStudent.id << "  " << tempStudent.name << "   " << tempStudent.cgpa << "   " << tempStudent.Rank << endl;
    }

    stfr.close();
    return 0;
}