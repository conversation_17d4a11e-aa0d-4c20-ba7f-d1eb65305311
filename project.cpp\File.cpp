#include <iostream>
#include <fstream>
#include <cstring>
#include <string>
#include <iomanip>
#include <sstream>

using namespace std;

struct students {
    int id, Rank;
    char name[50];  // Fixed-size array for binary storage
    float cgpa;
};

int main() {
    int n;
    cout << "How many students? ";
    cin >> n;

    students* s = new students[n];
    for (int i = 0; i < n; ++i) {
        cout << "Enter student " << i + 1 << " id: ";
        cin >> s[i].id;

        cout << "Enter student " << i + 1 << " name: ";
        cin.ignore();
        memset(s[i].name, 0, sizeof(s[i].name));
        cin.getline(s[i].name, 50);

        cout << "Enter student " << i + 1 << " cgpa: ";
        cin >> s[i].cgpa;
    }

    // Rank calculation
    for (int i = 0; i < n; i++) {
        s[i].Rank = 1;
        for (int j = 0; j < n; j++) {
            if (s[j].cgpa > s[i].cgpa) s[i].Rank++;
        }
    }

    // Write to binary file
    ofstream stf("Student.dat", ios::binary | ios::app);
    if (!stf) {
        cout << "Error opening file for writing!" << endl;
        delete[] s;
        return 1;
    }
    for (int j = 0; j < n; j++) {
        stf.write((char*)(&s[j]), sizeof(s[j]));
    }
    stf.close();

    // Read last student record
    ifstream stfi("Student.dat", ios::binary | ios::in);
    if (!stfi) {
        cout << "Error opening file for reading!" << endl;
        delete[] s;
        return 1;
    }

    students lastStudent;
    stfi.seekg(-static_cast<int>(sizeof(lastStudent)), ios::end);
    stfi.read((char*)(&lastStudent), sizeof(lastStudent));
    stfi.close();

    // Format last student record into a string
    ostringstream oss;
    oss << lastStudent.id << " " << lastStudent.name << " "
        << fixed << setprecision(2) << lastStudent.cgpa << " " << lastStudent.Rank;
    string lastLine = oss.str();

    int midPosition = lastLine.length() / 2;
    cout << "\nThe length of the last record: " << lastLine.length() << endl;
    cout << "The middle position of the last record: " << midPosition << endl;
    cout << "Middle character of last record: " << lastLine[midPosition] << endl;

    // Display all records
    ifstream stfr("Student.dat", ios::binary | ios::in);
    students tempStudent;

    cout << "\n" << left << setw(5) << "Id" << setw(10) << "Name" 
         << setw(8) << "CGPA" << setw(6) << "Rank" << endl;

    while (stfr.read((char*)(&tempStudent), sizeof(tempStudent))) {
        cout << left << setw(5) << tempStudent.id 
             << setw(10) << tempStudent.name 
             << fixed << setprecision(2) << setw(8) << tempStudent.cgpa 
             << setw(6) << tempStudent.Rank << endl;
    }
    stfr.close();

    delete[] s;
    return 0;
}
