#include <iostream>
#include <fstream>

using namespace std;

struct Student {
    int roll;
    char name[25];
    float marks;
} stud;

void getdata() {
    cout << "\n\nEnter Roll: ";
    cin >> stud.roll;
    cin.ignore();  // Flush newline from buffer
    cout << "Enter Name: ";
    cin.getline(stud.name, 25);
    cout << "Enter Marks: ";
    cin >> stud.marks;
}

void AddRecord() {
    fstream outf;
    outf.open("Student", ios::app | ios::binary);
    if(outf.is_open()){
       cout << "File opened successfully.";
    }
     else{
        cerr<<"Error opening file.";
        return;
     }
    getdata();
    outf.write((char*)&stud, sizeof(stud));
    outf.close();

     
    }


int main() {
    char ch;

    do {
        AddRecord();
        cout << "\nWant to add more (y/n): ";
        cin.ignore();           // Clear leftover newline
        cin.get(ch);
    } while (ch == 'y' || ch == 'Y');

    cout << "\nData written successfully...\n";
    
   
    return 0;
}
