#include <iostream>
#include <fstream>
#include <vector>

using namespace std;

struct employee{
  int number;
  string name;
  double hours,rate, grossPay,netPay,tax=0.15,pension=0.07;
  
};

template<typename T>
T read(vector<employee> E,int n){
cout<<"How many employees? ";
cin>>n;
for(int i=0,i<n;i++){
  cout<<"Enter details for employee " << i+1 <<": ";
  cout<<"Enter number: ";
  cin>>E.number;
  cout<<"Enter name: ";
  cin.ignore();
  getline(cin,E.name);
  cout<<"Enter Rate: ";
  cin>>E.rate;
  cout"Enter hours: ";
  cin>>E.hours;
}
}

T printAndCalculate(vector<employee> E,int n){

}

int main(){
  
  
  return 0;
}