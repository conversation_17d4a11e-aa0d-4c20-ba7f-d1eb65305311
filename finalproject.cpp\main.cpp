#include <iostream>
#include <fstream>
#include <vector>

using namespace std;

struct employee{
  int number;
  string name;
  double hours, rate, grossPay, netPay;
  double tax = 0.15;
  double pension = 0.07;
};

void readEmployees(vector<employee>& E, int n){
  for(int i = 0; i < n; i++){
    cout << "Enter details for employee " << i+1 << ": " << endl;
    cout << "Enter number: ";
    cin >> E[i].number;
    cout << "Enter name: ";
    cin.ignore();
    getline(cin, E[i].name);
    cout << "Enter Rate: ";
    cin >> E[i].rate;
    cout << "Enter hours: ";
    cin >> E[i].hours;
  }
}

void printAndCalculate(vector<employee>& E, int n){
  cout << "\nEmployee Details:" << endl;
  cout << "Number\tName\t\tHours\tRate\tGross Pay\tTax\tPension\tNet Pay" << endl;
  cout << "------------------------------------------------------------------------" << endl;

  for(int i = 0; i < n; i++){
    E[i].grossPay = E[i].hours * E[i].rate;
    double taxAmount = E[i].grossPay * E[i].tax;
    double pensionAmount = E[i].grossPay * E[i].pension;
    E[i].netPay = E[i].grossPay - taxAmount - pensionAmount;

    cout << E[i].number << "\t" << E[i].name << "\t\t" << E[i].hours << "\t"
         << E[i].rate << "\t" << E[i].grossPay << "\t\t" << taxAmount << "\t"
         << pensionAmount << "\t" << E[i].netPay << endl;
  }
}

int main(){
  int n;
  cout << "How many employees? ";
  cin >> n;

  vector<employee> employees(n);

  readEmployees(employees, n);
  printAndCalculate(employees, n);

  return 0;
}