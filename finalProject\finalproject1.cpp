#include <iostream>
#include <unordered_map>
#include <string>
#include <cctype>  // For character checks
#include <iomanip> // For setw
#include <regex>   // For regex password validation
#include <cstdlib> // For random number generation
#include <ctime>   // For seeding the random number generator

using namespace std;

//Function prototypes
void patientManagement();
void appointmentScheduling();
void userAuthentication();
void staffManagement();

//Functions used in user authentication
bool isStrongPassword(const string& password);
bool authenticate(int userId, const string& password);
void registerUser(int userId, const string& name, const string& password, const string& role);
void loginUser();
void registerNewUser();

//Functions used in patient management
void add();//Add new patients
void displayPatients();//Display all patients' informatio
void search();//To display the patient's information by searching its name

//Functions used in appiontment scheduling
void bookAppoitment();//Book an appiontment by entering patient name and slot number
void cancelAppointment();//Cancel an appointment by clearing slots
void displayAppointments();// Display all appointment slots and their status (booked or available).

//Functions used in staff management
void markAttendance();
void displayAttendance();

/**
 * Displays the admin panel interface.
 * This function provides the interface for the admin panel where
 * administrative tasks can be performed. It may include options
 * for managing patients, viewing patients informations and records,
 * and staff management.
 */
void adminPanel();

/**
 * Displays the patient panel interface.
 * This function provides the interface for the patient panel where
 * patients can book appointments, view their appointments, and
 * view their medical records.
 */
void patientPanel();

// variables
int choice;//Global variable to store user choice

// variable for patient management
int num = 0, Gender, n, flag = 1;
string pname[100];
string gender[100];
int pid[100];
int age[100];
float weight[100];
float height[100];
string p_discription[100];
string name;
char x;

//variables for appointment scheduling
const int daysInWeek = 7;
const int appointmentsPerDay = 10;
string slots[daysInWeek][appointmentsPerDay];
const string daysOfWeek[daysInWeek] = {"Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"};
const string times[appointmentsPerDay] = {"09:00 AM", "09:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM", "12:00 PM", "12:30 PM", "01:00 PM", "01:30 PM"};
// const and structure for appointment scheduling
struct Appointment
{
    int id;
    string patientName;
};
Appointment appointments[daysInWeek][appointmentsPerDay];

// const and structure for staff attendance
const int MAX_STAFF = 10;

struct Staff
{
    string name;
    bool isPresent; // true = present, false = absent
};

//Functions used in appiontment scheduling
void initialSlots()
{
    for (int day = 0; day < daysInWeek; ++day)
    {
        for (int slot = 0; slot < appointmentsPerDay; ++slot)
        {
            slots[day][slot] = "Available";
            appointments[day][slot] = {0, ""};
        }
    }
}

// Generates a random ID for the patient between 1-9999

int idGenerator()
{
    return rand() % 10000 + 1;
}

// Function to book an appointment

void bookAppointment()
{
    int dayChoice, timeChoice;

    cout << "\n=============Appointment Booking=============\n\n";

    while (true)
    {

        cout << "Available days:\n";
        for (int i = 0; i < daysInWeek; ++i)
        {
            cout << i + 1 << ". " << daysOfWeek[i] << "\n";
        }

        cout << "Enter the number of the day you want to book: ";
        cin >> dayChoice;

        if (dayChoice < 1 || dayChoice > daysInWeek)
        {
            cout << "Invalid day choice. Please select a valid day (1-7).\n";
            continue;
        }

        cout << "\nAvailable appointment times on " << daysOfWeek[dayChoice - 1] << ":\n";

        bool slotsAvailable = false;
        for (int i = 0; i < appointmentsPerDay; ++i)
        {
            if (slots[dayChoice - 1][i] == "Available")
            {
                slotsAvailable = true;
                cout << i + 1 << ". " << times[i] << "\n";
            }
        }

        if (!slotsAvailable)
        {
            cout << "No available time slots for " << daysOfWeek[dayChoice - 1] << ".\n";
            continue;
        }

        cout << "Enter the number of the time slot you want to book: ";
        cin >> timeChoice;

        if (timeChoice < 1 || timeChoice > appointmentsPerDay)
        {
            cout << "Invalid time slot choice. Please select a valid time slot.\n";
            continue;
        }

        if (slots[dayChoice - 1][timeChoice - 1] != "Available")
        {
            cout << "Appointment already booked. Please choose a different time.\n";
            continue;
        }

        string patientName;
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        getline(cin, patientName);

        int appointmentID = idGenerator();

        slots[dayChoice - 1][timeChoice - 1] = patientName;
        appointments[dayChoice - 1][timeChoice - 1] = {appointmentID, patientName};

        cout << "Appointment booked successfully for " << daysOfWeek[dayChoice - 1] << " at " << times[timeChoice - 1] << "\n";
        cout << "Your Appointment ID is: " << appointmentID << ". Please do not lose or share your ID with others for private information.\n";

        break;
    }
}

// Function to cancel appointment

void cancelAppointment()
{
    int idToCancelAppointment;

    cout << "\n=============Appointment Cancellation=============\n\n";

    cout << "Enter your Appointment ID to cancel: ";
    cin >> idToCancelAppointment;

    bool found = false;

    for (int day = 0; day < daysInWeek; ++day)
    {
        for (int slot = 0; slot < appointmentsPerDay; ++slot)
        {
            if (appointments[day][slot].id == idToCancelAppointment)
            {
                found = true;
                cout << "Appointment found on " << daysOfWeek[day] << " at " << times[slot] << ".\n";
                cout << "Canceling your appointment...\n";

                slots[day][slot] = "Available";
                appointments[day][slot] = {0, ""}; // Reset appointment details
                cout << "Appointment canceled successfully.\n";
                break;
            }
        }
        if (found)
            break;
    }

    if (!found)
    {
        cout << "Appointment ID not found. Please check your ID and try again.\n";
    }
}

// Function to display appointments

void displayAppointments()
{
    cout << "\n=============Appointment Display=============\n\n";
    for (int day = 0; day < daysInWeek; ++day)
    {
        cout << "==========================\n";
        cout << daysOfWeek[day] << ":\n";
        for (int slot = 0; slot < appointmentsPerDay; ++slot)
        {
            cout << "  " << times[slot] << ": " << (slots[day][slot] == "Available" ? "Available" : "Booked") << "\n";
        }
    }
}

// Function to manage attendance
void markAttendance(Staff staffList[], int size, const string &name, bool status)
{
    for (int i = 0; i < size; ++i)
    {
        if (staffList[i].name == name)
        {
            staffList[i].isPresent = status;
            cout << name << " marked as " << (status ? "Present" : "Absent") << "." << endl;
            return;
        }
    }
    cout << "Staff member " << name << " not found!" << endl;
}
// Function to display attendance
void displayAttendance(const Staff staffList[], int size)
{
    cout << "\nAttendance Status:\n";
    for (int i = 0; i < size; ++i)
    {
        cout << "Name: " << staffList[i].name
             << ", Status: " << (staffList[i].isPresent ? "Present" : "Absent") << endl;
    }
}

// Functions used in user authentication
// A map to store user credentials (ID, Password, Name, Role)
unordered_map<int, pair<string, pair<string, string>>> userCredentials; // User ID -> {Name, {Password, Role}}

// Function to check if the password is strong
bool isStrongPassword(const string &password)
{
    if (password.length() < 8)
        return false;
    bool hasLower = false, hasUpper = false, hasDigit = false, hasSpecial = false;

    for (char c : password)
    {
        if (islower(c))
            hasLower = true;
        if (isupper(c))
            hasUpper = true;
        if (isdigit(c))
            hasDigit = true;
        if (!isalnum(c))
            hasSpecial = true;
    }
    return hasLower && hasUpper && hasDigit && hasSpecial;
}

// Function to register a new user
void registerUser(int userId, const string &name, const string &password, const string &role)
{
    userCredentials[userId] = make_pair(name, make_pair(password, role));
    cout << "User " << name << " (" << role << ") registered successfully with ID: " << userId << endl;
}

// Function to authenticate a user
bool authenticate(int userId, const string &password)
{
    return userCredentials.count(userId) && userCredentials[userId].second.first == password;
}

void loginUser()
{
        int userId;
        string password;

        cout << "Enter your User ID: ";
        cin >> userId;

        cout << "Enter your password: ";
        cin >> password;

        if (authenticate(userId, password))
        {
            string role = userCredentials[userId].second.second;
            cout << "Login successful! Welcome, " << userCredentials[userId].first << "!\n";

            if (role == "Admin")
            {
                cout << "Please enter your Admin ID to proceed: ";
                int adminId;
                cin >> adminId;
                if (adminId == userId)
                {
                adminPanel();
                }
                else
                {
                cout << "Invalid Admin ID.\n";
                }
            }
            else
            {
               // Redirect to Patient Panel if the role is not Admin
               patientPanel();
            }
        }
        else
        {
            cout << "Incorrect User ID or password.\n";
        }
    }

    // Function to get user input for registration
    void registerNewUser()
    {
        string name, password, role;
        int userId;

        cout << "Enter your name: ";
        cin.ignore(numeric_limits<streamsize>::max(), '\n');
        getline(cin, name);

        cout << "Enter your password: ";
        cin >> password;

        if (!isStrongPassword(password))
        {
            cout << "Password must contain at least 8 characters, including uppercase, lowercase, digit, and special character.\n";
            return;
        }

        cout << "Enter your role (Admin/Patient): ";
        cin >> role;

       do
        {
            userId = rand() % 9000 + 1000; // Generate a unique 4-digit ID
        } while (userCredentials.count(userId));

        registerUser(userId, name, password, role);
    }

//Functions used in patient management
    // Function to patient management
    void add()
    {
    b:
        cout << "Please enter the number of patients you want to add:\n";
        cin >> n;
        if (cin.fail() || n < 0)
        {
            cin.clear();
            cin.ignore();
            cout << "Enter number only" << endl;
            goto b;
        }
        num += n;
        cout << "PLease enter their details:\n";
        for (int i = num - n; i < num; i++)
        {
            cout << "Enter the name of the " << i + 1 << " patient: ";
            cin.ignore();
            getline(cin, pname[i]);
        i:
            cout << "Enter gender: \n1. Male\n2. Female" << endl;
            cin >> Gender;
            if (cin.fail() || Gender < 1 || Gender > 2)
            {
                cin.clear();
                cin.ignore();
                cout << "Enter proper input!" << endl;
                goto i;
            }
            if (Gender == 1)
                gender[i] = "Male";
            else
                gender[i] = "Female";
        j:
            cout << "Enter the patient ID: " << endl;
            cin >> pid[i];
            if (cin.fail())
            {
                cin.clear();
                cin.ignore();
                cout << "Enter proper input" << endl;
                goto j;
            }
        l:
            cout << "Enter the Age of the patient: ";
            cin >> age[i];
            if (cin.fail() || age[i] < 0)
            {
                cin.clear();
                cin.ignore();
                cout << "Enter proper input!" << endl;
                goto l;
            }
        m:
            cout << "Enter the Weight of the patient: ";
            cin >> weight[i];
            if (cin.fail() || weight[i] < 0)
            {
                cin.clear();
                cin.ignore();
                cout << "Enter proper input!" << endl;
                goto m;
            }
        n:
            cout << "Enter the Height of the patient: ";
            cin >> height[i];
            if (cin.fail() || height[i] < 0)
            {
                cin.clear();
                cin.ignore();
                cout << "Enter proper input!" << endl;
                goto n;
            }
            cout << "Enter the medical history of the patient: ";
            cin.ignore();
            getline(cin, p_discription[i]);
        }
    }

    void display()
    {
        cout << "\n=== Patient Medical Records ===\n";
        cout << left << setw(20) << "Name" << setw(10) << "Gender" << setw(20) << "ID" << setw(10) << "Age" << setw(10) << "Weight" << setw(10) << "Height" << setw(20) << "Medical history" << "\n";
        cout << string(95, '-') << "\n";

        for (int i = 0; i < num; i++)
        {
            cout << left << setw(20) << pname[i]
                 << setw(10) << gender[i]
                 << setw(20) << pid[i]
                 << setw(10) << age[i] << setw(10) << weight[i] << setw(10) << height[i] << setw(20) << fixed << p_discription[i] << "\n";
        }
    }

    void search()
    {
        cout << "Enter the name of the patient: ";
        cin.ignore();
        getline(cin, name);
        for (int i = 0; i < num; i++)
        {
            if (name == pname[i])
            {
                cout << "\n=== Patient Medical Records ===\n";
                cout << left << setw(20) << "Name" << setw(10) << "Gender" << setw(20) << "ID" << setw(10) << "Age" << setw(10) << "Weight" << setw(10) << "Height" << setw(20) << "Medical history" << "\n";
                cout << string(95, '-') << "\n";
                cout << left << setw(20) << pname[i]
                     << setw(10) << gender[i]
                     << setw(20) << pid[i]
                     << setw(10) << age[i] << setw(10) << setprecision(2) << weight[i] << setw(10) << setprecision(2) << height[i] << setw(20) << fixed << p_discription[i] << "\n";
                break;
            }
        }
    }
    void staffManagement() {
    Staff staffList[MAX_STAFF] = {
        {"Aschal<<ew", false}, {"Emebet", false}, {"Solomon", false}, {"Ahmed", false}, {"Hanan", false}, {"Askalech", false}, {"Tsion", false}, {"Mulugeta", false}, {"Alemayew", false}, {"Abebe", false}};
    int choice;
    string name;
    bool status;
    do
    {
        cout << "\n\n\n\t\t----- Staff Attendance System-----";
        cout << "\n\t\t1. Mark Attendance"
             << "\n\t\t2. Display Attendance"
             << "\n\t\t3. Exit"
             << "\n\t\t4. Back to Main Menu"
             << "\n\t\tEnter your choice: ";
        cin >> choice;

        switch (choice)
        {
        case 1:
            cout << "Enter staff name: ";
            cin.ignore();
            getline(cin, name);
            cout << "Enter status (1 for Present, 0 for Absent): ";
            cin >> status;
            markAttendance(staffList, MAX_STAFF, name, status);
            break;
        case 2:
            displayAttendance(staffList, MAX_STAFF);
            break;
        case 3:
            break;
        case 4:
            return;
        default:
            cout << "Invalid choice. Please try again.\n";
        }
    } while (choice != 3);
}

void patientManagement() {
    int choice;
 do{
        a:
        cout<<"\n\n\n\t\t----Patient Management Menu----";
        cout<<"\n\t\t1.Add Patients";
        cout<<"\n\t\t2.View Patients' Information";
        cout<<"\n\t\t3.Search for a patient";
        cout<<"\n\t\t4.Back to Main Menu";
        cout<<"\n\t\t5.Exit";

        cout<<"\n\n\t\tEnter your choice: ";
        cin>>choice;

        if(cin.fail()){
		cin.clear(); cin.ignore();
		cout<<"Enter number only"<<endl;
		goto a;
        }
        switch(choice)
        {
            case 1:
               add();
               break;
            case 2:
               displayPatients();
               break;
            case 3:
               search();
		       break;
            case 4:
               return;
            case 5:
               break;
            default:
                cout<<"\n\t\t\tInvalid input!"
                <<"\n\t\t\tPlease select the valid option from the given menu.";
        }
    }
    while (choice != 5);
};

// Function to handle admin functionality
void adminPanel()
{
    do{
        cout << "n\n\n\t\t----Welcome to the Admin Panel. (Admin functionality goes here)----";
        cout << "\n\t\t1. Patient Management"
             << "\n\t\t2. Staff Attendance"
             << "\n\t\t3. Display Patients appointment"
             << "\n\t\t4. Exit";
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        cout<<"Enter your choice: ";
        cin >> choice;

        switch (choice)
        {
        case 1:
           patientManagement();
            break;
        case 2:
            staffManagement();
            break;
        case 3:
            displayAppointments();
            break;
        case 4:
            cout << "Exiting Admin Panel.\n";
            return;
        default:
            cout << "Invalid choice. Please try again.\n";
            break;
        }
    } while (choice != 3);
}

void patientPanel(){
   do{
        cout<<"\n\n\n\t\t----Welcome to the Patient Portal! (Patient functionality goes here)----";
        cout<<"\n\t\t1. Book an Appointment";
        cout<<"\n\t\t2. Cancel an Appointment";
        cout<<"\n\t\t3. Exit";
        cin.ignore(numeric_limits<streamsize>::max(), '\n');

        cout<<"\n\n\t\tEnter your choice: ";
        cin>>choice;

        switch(choice)
        {
            case 1:
                bookAppointment();
                break;
            case 2:
                cancelAppointment();
                break;
            case 3:
                cout<<"Exiting patient portal";
                break;
            default:
                cout<<"\n\t\t\tInvalid input!"
                <<"\n\t\t\tPlease select the valid option from the given menu.";
        }
    }
    while (choice != 3);
}

// Function to display the main menu
    void displayMenu()
    {
       cout<<"\n\n\t\t\t>>>>>>>>>>>>>";
       cout<<"\n\t\t\t* MAIN MENU *";
       cout<<"\n\t\t\t>>>>>>>>>>>>>";

       cout << "\n\n\n\t\t1. Register"
             << "\n\t\t2. Login"
             << "\n\t\t3. Exit"
             << "\n\n\t\tChoose an option: ";
    }

    int main()
    {
        cout<<"\n\t\t\t\t\t------Hospital management system--------";
        cout<<"\n\n\t\t\t*******************************"
             <<"\n\n\t\t\t* Welcome to miracle hospital *"
             <<"\n\n\t\t\t*******************************";
        cout<<"\n\n\t\tPlease log in to continue";

        srand(static_cast<unsigned int>(time(0))); // Seed for random number generation

    // Initialize appointment slots
        initialSlots();

        while (true)
        {
            displayMenu();
            cin >> choice;

            if (choice == 1)
            {
                registerNewUser();
            }
            else if (choice == 2)
            {
                loginUser();
            }
            else if (choice == 3)
            {
                cout << "Exiting the system. Goodbye!\n";
                break;
            }
            else
            {
                cout << "Invalid choice. Please try again.\n";
            }
        }

        return 0;
    }
