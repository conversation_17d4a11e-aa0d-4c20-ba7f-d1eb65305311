
#include <iostream>
#include <fstream>
#include <iomanip>

using namespace std;

struct Student {
    int roll;
    char name[25];
    float marks;
} stud;

void getdata() {
    cout << "\n\nEnter Roll: ";
    cin >> stud.roll;
    cin.ignore();  // Flush newline from buffer
    cout << "Enter Name: ";
    cin.getline(stud.name, 25);
    cout << "Enter Marks: ";
    cin >> stud.marks;
}

void AddRecord() {
    fstream outf;
    outf.open("Student.txt", ios::app);
    if(outf.is_open()){
       cout << "File opened successfully.";
    }
     else{
        cerr<<"Error opening file.";
        return;
     }
    getdata();
    outf.write((char*)&stud, sizeof(stud));
    outf.close();
    }

void DisplayRecords() {
    ifstream inf;
    inf.open("Student.txt", ios::app);
    if(!inf.is_open()) {
        cerr << "Error opening file for reading.";
        return;
    }

    cout << "\nStudent Records:\n";
    cout << "================\n";

    while(inf.read(reinterpret_cast<char*>(&stud), sizeof(stud))) {
        cout << "Roll: " << stud.roll << endl;
        cout << "Name: " << stud.name << endl;
        cout << "Marks: " << stud.marks << endl;
        cout << "----------------\n";
    }

    inf.close();
}

int main() {
    char ch;

    do {
        AddRecord();
        cout << "\nWant to add more (y/n): ";
        cin.ignore();           // Clear leftover newline
        cin.get(ch);
    } while (ch == 'y' || ch == 'Y');

    cout << "\nData written successfully...\n";

    DisplayRecords();

    return 0;
}
