// C++ code for std::basic_istream::peek()
#include <iostream>
#include <bits/stdc++.h>
using namespace std;

// main method
int main()
{
    istringstream gfg("GeeksforGeeks");

    char c1 = gfg.putback();
    char c2 = gfg.get();
    char c3 = gfg.get();
    char c4=  gfg.get();
    char c5=  gfg.get();
    char c6=  gfg.get();

    cout << "The first character is: "
         << c1 << endl
         << " and the next is: "
         << c6 << endl;
}