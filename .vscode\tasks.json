{"tasks": [{"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "D:\\MinGW\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}, {"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "D:/MinGW/bin/g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe", ""], "options": {"cwd": "D:/MinGW/bin"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "compiler: D:/MinGW/bin/g++.exe"}], "version": "2.0.0"}